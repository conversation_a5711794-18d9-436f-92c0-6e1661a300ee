// netlify/functions/test-api.js
// Simple test function to verify API keys and environment setup

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // <PERSON>le preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow GET method for this test
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        // Check environment and API key configuration
        const apiKeys = {
            'FIREWORKS_API_KEY': !!process.env.FIREWORKS_API_KEY,
            'NOVITA_API_KEY': !!process.env.NOVITA_API_KEY,
            'OPENROUTER_API_KEY': !!process.env.OPENROUTER_API_KEY,
            'GROQ_API_KEY': !!process.env.GROQ_API_KEY,
            'HUGGINGFACE_API_TOKEN': !!process.env.HUGGINGFACE_API_TOKEN,
            'GPTZERO_API_KEY': !!process.env.GPTZERO_API_KEY,
        };

        const configuredKeys = Object.entries(apiKeys)
            .filter(([key, configured]) => configured)
            .map(([key]) => key);

        const missingKeys = Object.entries(apiKeys)
            .filter(([key, configured]) => !configured)
            .map(([key]) => key);

        // Test basic functionality
        const testResult = {
            status: 'success',
            timestamp: new Date().toISOString(),
            environment: {
                NODE_ENV: process.env.NODE_ENV || 'unknown',
                NETLIFY: process.env.NETLIFY || 'false',
                NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'not set'
            },
            apiKeys: {
                configured: configuredKeys,
                missing: missingKeys,
                total: configuredKeys.length,
                hasDeepSeekAccess: !!(process.env.FIREWORKS_API_KEY || process.env.NOVITA_API_KEY || process.env.OPENROUTER_API_KEY),
                hasFallbackAccess: !!(process.env.GROQ_API_KEY || process.env.HUGGINGFACE_API_TOKEN)
            },
            recommendations: []
        };

        // Add recommendations based on configuration
        if (testResult.apiKeys.total === 0) {
            testResult.recommendations.push('⚠️ No API keys configured. Humanization will use basic pattern-based fallback only.');
        } else if (!testResult.apiKeys.hasDeepSeekAccess) {
            testResult.recommendations.push('💡 Consider adding DeepSeek-R1 API keys (FIREWORKS_API_KEY, NOVITA_API_KEY, or OPENROUTER_API_KEY) for best humanization results.');
        } else {
            testResult.recommendations.push('✅ DeepSeek-R1 access configured. You should get excellent humanization results.');
        }

        if (!testResult.apiKeys.hasFallbackAccess && testResult.apiKeys.hasDeepSeekAccess) {
            testResult.recommendations.push('💡 Consider adding fallback API keys (GROQ_API_KEY or HUGGINGFACE_API_TOKEN) for better reliability.');
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(testResult, null, 2),
        };

    } catch (error) {
        console.error('Test API error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                status: 'error',
                message: 'Internal server error during API test',
                error: error.message,
                timestamp: new Date().toISOString()
            }),
        };
    }
};
