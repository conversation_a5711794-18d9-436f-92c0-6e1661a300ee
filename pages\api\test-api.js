// pages/api/test-api.js
// Test API endpoint for localhost development

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

    // <PERSON>le preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    // Only allow GET method for this test
    if (req.method !== 'GET') {
        return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }

    try {
        // Check environment and API key configuration
        const apiKeys = {
            'FIREWORKS_API_KEY': !!process.env.FIREWORKS_API_KEY,
            'NOVITA_API_KEY': !!process.env.NOVITA_API_KEY,
            'OPENROUTER_API_KEY': !!process.env.OPENROUTER_API_KEY,
            'GROQ_API_KEY': !!process.env.GROQ_API_KEY,
            'HUGGINGFACE_API_TOKEN': !!process.env.HUGGINGFACE_API_TOKEN,
            'GPTZERO_API_KEY': !!process.env.GPTZERO_API_KEY,
        };

        const configuredKeys = Object.entries(apiKeys)
            .filter(([key, configured]) => configured)
            .map(([key]) => key);

        const missingKeys = Object.entries(apiKeys)
            .filter(([key, configured]) => !configured)
            .map(([key]) => key);

        // Test basic functionality
        const testResult = {
            status: 'success',
            timestamp: new Date().toISOString(),
            environment: {
                NODE_ENV: process.env.NODE_ENV || 'unknown',
                NETLIFY: process.env.NETLIFY || 'false',
                NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'not set'
            },
            apiKeys: {
                configured: configuredKeys,
                missing: missingKeys,
                total: configuredKeys.length,
                hasDeepSeekAccess: !!(process.env.FIREWORKS_API_KEY || process.env.NOVITA_API_KEY || process.env.OPENROUTER_API_KEY),
                hasFallbackAccess: !!(process.env.GROQ_API_KEY || process.env.HUGGINGFACE_API_TOKEN)
            },
            recommendations: []
        };

        // Add recommendations based on configuration
        if (testResult.apiKeys.total === 0) {
            testResult.recommendations.push('⚠️ No API keys configured. Humanization will use basic pattern-based fallback only.');
        } else if (!testResult.apiKeys.hasDeepSeekAccess) {
            testResult.recommendations.push('💡 Consider adding DeepSeek-R1 API keys (FIREWORKS_API_KEY, NOVITA_API_KEY, or OPENROUTER_API_KEY) for best humanization results.');
        } else {
            testResult.recommendations.push('✅ DeepSeek-R1 access configured. You should get excellent humanization results.');
        }

        if (!testResult.apiKeys.hasFallbackAccess && testResult.apiKeys.hasDeepSeekAccess) {
            testResult.recommendations.push('💡 Consider adding fallback API keys (GROQ_API_KEY or HUGGINGFACE_API_TOKEN) for better reliability.');
        }

        return res.status(200).json(testResult);

    } catch (error) {
        console.error('Test API error:', error);
        
        return res.status(500).json({
            status: 'error',
            message: 'Internal server error during API test',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
}
