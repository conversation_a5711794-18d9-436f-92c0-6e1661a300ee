/**
 * Get the correct API endpoint based on the environment
 */
const getApiEndpoint = () => {
    // Check if we're in a static export environment (Netlify)
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;

        // If deployed on Netlify, use Netlify Functions
        if (hostname.includes('netlify.app') || hostname.includes('netlify.com')) {
            return '/.netlify/functions/process';
        }

        // For custom domains deployed on Netlify, also use Netlify Functions
        // You can add your custom domain here if needed
        if (process.env.NEXT_PUBLIC_APP_URL && process.env.NEXT_PUBLIC_APP_URL.includes('netlify')) {
            return '/.netlify/functions/process';
        }
    }

    // For localhost and other environments, use Next.js API routes
    return '/api/process';
};

export const processTextApi = async (data) => {
    const endpoint = getApiEndpoint();

    console.log(`🔗 Using API endpoint: ${endpoint}`);

    const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'An unknown error occurred' }));
        throw new Error(errorData.message || `Error: ${response.status}`);
    }
    return response.json();
};

/**
 * Test API configuration and connectivity
 */
export const testApiConfiguration = async () => {
    const testEndpoint = typeof window !== 'undefined' &&
        (window.location.hostname.includes('netlify.app') || window.location.hostname.includes('netlify.com'))
        ? '/.netlify/functions/test-api'
        : '/api/test-api';

    console.log(`🧪 Testing API configuration at: ${testEndpoint}`);

    try {
        const response = await fetch(testEndpoint, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`Test API request failed: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API configuration test failed:', error);
        throw error;
    }
};
