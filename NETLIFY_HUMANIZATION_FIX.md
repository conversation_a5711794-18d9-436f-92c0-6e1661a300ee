# 🔧 Netlify Humanization Performance Fix

## 🎯 **Problem Identified**

Your localhost has much better humanization results than Netlify because:

1. **Localhost**: Uses Next.js API routes (`/api/process`) with full Node.js capabilities and API access
2. **Netlify**: Uses static export which can't run server-side code, but your frontend was calling the wrong endpoint

## ✅ **Solution Applied**

### 1. **Fixed API Client** (`src/utils/apiClient.js`)
- Updated to automatically detect environment
- Uses `/.netlify/functions/process` on Netlify
- Uses `/api/process` on localhost
- Added logging for debugging

### 2. **Required Netlify Environment Variables**

You need to configure these API keys in your **Netlify Dashboard → Site Settings → Environment Variables**:

#### 🔑 **Essential API Keys for Advanced Humanization**
```bash
# Primary DeepSeek-R1 API Keys (Choose at least one)
FIREWORKS_API_KEY=your_fireworks_api_key_here
NOVITA_API_KEY=your_novita_api_key_here  
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Fallback LLM APIs
GROQ_API_KEY=your_groq_api_key_here
HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# AI Detection APIs (Optional but recommended)
GPTZERO_API_KEY=your_gptzero_api_key_here
ORIGINALITY_API_KEY=your_originality_api_key_here
```

#### 🏗️ **Core Application Variables**
```bash
NODE_ENV=production
NETLIFY=true
NEXTAUTH_SECRET=your_super_strong_random_secret_here
NEXTAUTH_URL=https://your-site.netlify.app
NEXT_PUBLIC_APP_URL=https://your-site.netlify.app
NEXT_PUBLIC_APP_NAME=GhostLayer
```

## 🚀 **Deployment Steps**

### Step 1: Configure Environment Variables
1. Go to **Netlify Dashboard** → Your Site → **Site Settings** → **Environment Variables**
2. Add all the API keys listed above
3. **Important**: Use your actual API keys, not placeholder values

### Step 2: Get API Keys

#### **DeepSeek-R1 API Keys** (Primary - Choose one):
- **Fireworks AI**: https://fireworks.ai/api-keys
- **Novita AI**: https://novita.ai/dashboard/api-keys  
- **OpenRouter**: https://openrouter.ai/keys

#### **Fallback API Keys**:
- **Groq**: https://console.groq.com/keys
- **Hugging Face**: https://huggingface.co/settings/tokens

### Step 3: Redeploy
1. After adding environment variables, trigger a new deployment
2. Go to **Deploys** → **Trigger Deploy** → **Deploy Site**

## 🧪 **Testing the Fix**

### Test Locally:
```bash
npm run dev
# Should use /api/process endpoint
```

### Test on Netlify:
1. Deploy with environment variables
2. Check browser console for: `🔗 Using API endpoint: /.netlify/functions/process`
3. Verify humanization quality matches localhost

## 🔍 **Debugging**

### Check Netlify Function Logs:
1. Go to **Netlify Dashboard** → **Functions** → **process**
2. View real-time logs to see API calls and errors

### Common Issues:
- **"API key not found"**: Environment variables not configured
- **"All humanization methods failed"**: No valid API keys provided
- **"Function timeout"**: API provider issues or rate limits

## 📊 **Expected Performance**

With proper API keys configured:
- **Localhost**: ≤10% AI detection with DeepSeek-R1
- **Netlify**: ≤10% AI detection with DeepSeek-R1 (same performance)

## 🔄 **Fallback Chain**

The system will try in this order:
1. **DeepSeek-R1** (Fireworks/Novita/OpenRouter)
2. **Llama 3.1** (Groq/Fireworks)
3. **Falcon Models** (HuggingFace)
4. **Pattern-based** (Local processing)

## ⚡ **Quick Fix Checklist**

- [ ] Updated `src/utils/apiClient.js` (✅ Done)
- [ ] Added API keys to Netlify environment variables
- [ ] Redeployed site
- [ ] Tested humanization quality
- [ ] Checked function logs for errors

## 🆘 **If Still Not Working**

1. Check Netlify Function logs for specific errors
2. Verify API keys are valid and have sufficient credits
3. Test individual API providers using the test script:
   ```bash
   node test-falcon-humanization.js
   ```
